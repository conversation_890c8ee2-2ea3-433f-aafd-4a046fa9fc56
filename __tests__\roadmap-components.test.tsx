/**
 * Roadmap Components Test Suite
 * 
 * Tests for the gamified learning roadmap React components
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import '@testing-library/jest-dom'

// Mock Next.js router
const mockPush = jest.fn()
const mockBack = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
  }),
  useParams: () => ({ id: 'test-course-id', slug: 'test-course' }),
}))

// Mock Socket.io
jest.mock('@/hooks/use-socket', () => ({
  useSocket: () => ({
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
  }),
}))

// Mock toast notifications
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Import components after mocks
import RoadmapConfig from '@/components/admin/roadmap/roadmap-config'
import RoadmapVisualization from '@/components/student/roadmap/roadmap-visualization'
import MissionAchievements from '@/components/student/mission-achievements'

describe('Roadmap Configuration Component', () => {
  const mockProps = {
    courseId: 'test-course-id',
    hasRoadmap: false,
    roadmapTitle: '',
    roadmapDescription: '',
    missions: [],
    onUpdate: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders roadmap toggle', () => {
    render(<RoadmapConfig {...mockProps} />)
    
    expect(screen.getByText('Gamified Learning Roadmap')).toBeInTheDocument()
    expect(screen.getByText('Create an interactive, game-like learning journey for your students')).toBeInTheDocument()
  })

  test('enables roadmap configuration when toggled', async () => {
    render(<RoadmapConfig {...mockProps} />)
    
    const toggle = screen.getByRole('switch')
    fireEvent.click(toggle)
    
    await waitFor(() => {
      expect(mockProps.onUpdate).toHaveBeenCalledWith(
        expect.objectContaining({ hasRoadmap: true })
      )
    })
  })

  test('shows mission management when roadmap is enabled', () => {
    const enabledProps = { ...mockProps, hasRoadmap: true }
    render(<RoadmapConfig {...enabledProps} />)
    
    expect(screen.getByText('Learning Missions')).toBeInTheDocument()
    expect(screen.getByText('Add Mission')).toBeInTheDocument()
  })

  test('displays missions when available', () => {
    const propsWithMissions = {
      ...mockProps,
      hasRoadmap: true,
      missions: [
        {
          id: 'mission-1',
          title: 'Test Mission',
          description: 'A test mission',
          icon: '🎯',
          color: '#3B82F6',
          order: 1,
          isRequired: true,
          pointsReward: 100,
          estimatedTime: '30 minutes',
          contents: [],
          prerequisites: [],
        },
      ],
    }
    
    render(<RoadmapConfig {...propsWithMissions} />)
    
    expect(screen.getByText('Test Mission')).toBeInTheDocument()
    expect(screen.getByText('A test mission')).toBeInTheDocument()
    expect(screen.getByText('100 points')).toBeInTheDocument()
  })
})

describe('Roadmap Visualization Component', () => {
  const mockProps = {
    courseId: 'test-course-id',
    courseTitle: 'Test Course',
    roadmapTitle: 'Test Learning Journey',
    roadmapDescription: 'A test roadmap',
    missions: [
      {
        id: 'mission-1',
        title: 'Mission 1',
        description: 'First mission',
        icon: '🎯',
        color: '#3B82F6',
        order: 1,
        xPosition: 0,
        yPosition: 0,
        isRequired: true,
        pointsReward: 100,
        estimatedTime: '30 minutes',
        contents: [],
        prerequisites: [],
        progress: {
          id: 'progress-1',
          isStarted: false,
          isCompleted: false,
          completionRate: 0,
          pointsEarned: 0,
        },
      },
    ],
    userProgress: {
      totalPoints: 0,
      completedMissions: 0,
      currentStreak: 0,
    },
    onMissionClick: jest.fn(),
    onStartMission: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders roadmap header', () => {
    render(<RoadmapVisualization {...mockProps} />)
    
    expect(screen.getByText('Test Learning Journey')).toBeInTheDocument()
    expect(screen.getByText('A test roadmap')).toBeInTheDocument()
  })

  test('displays user progress stats', () => {
    render(<RoadmapVisualization {...mockProps} />)
    
    expect(screen.getByText('Points')).toBeInTheDocument()
    expect(screen.getByText('Completed')).toBeInTheDocument()
    expect(screen.getByText('Day Streak')).toBeInTheDocument()
  })

  test('renders mission nodes', () => {
    render(<RoadmapVisualization {...mockProps} />)
    
    expect(screen.getByText('Mission 1')).toBeInTheDocument()
    expect(screen.getByText('100')).toBeInTheDocument() // Points reward
  })

  test('handles mission click', async () => {
    render(<RoadmapVisualization {...mockProps} />)
    
    const missionNode = screen.getByText('Mission 1').closest('div')
    if (missionNode) {
      fireEvent.click(missionNode)
      
      await waitFor(() => {
        expect(mockProps.onMissionClick).toHaveBeenCalledWith(mockProps.missions[0])
      })
    }
  })

  test('shows completed mission state', () => {
    const completedProps = {
      ...mockProps,
      missions: [
        {
          ...mockProps.missions[0],
          progress: {
            id: 'progress-1',
            isStarted: true,
            isCompleted: true,
            completionRate: 100,
            pointsEarned: 100,
          },
        },
      ],
      userProgress: {
        totalPoints: 100,
        completedMissions: 1,
        currentStreak: 1,
      },
    }
    
    render(<RoadmapVisualization {...completedProps} />)
    
    expect(screen.getByText('100')).toBeInTheDocument() // Total points
    expect(screen.getByText('1')).toBeInTheDocument() // Completed missions
  })
})

describe('Mission Achievements Component', () => {
  const mockProps = {
    userId: 'test-user-id',
    onAchievementUnlocked: jest.fn(),
  }

  // Mock fetch for API calls
  global.fetch = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetch as jest.Mock).mockClear()
  })

  test('renders achievements header', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          achievements: { earned: [], available: [] },
          stats: {
            completedMissions: 0,
            totalPointsEarned: 0,
            consecutiveDays: 0,
          },
        },
      }),
    })
    
    render(<MissionAchievements {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Mission Achievements')).toBeInTheDocument()
    })
  })

  test('displays achievement stats', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          achievements: { earned: [], available: [] },
          stats: {
            completedMissions: 5,
            totalPointsEarned: 500,
            consecutiveDays: 3,
          },
        },
      }),
    })
    
    render(<MissionAchievements {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('500')).toBeInTheDocument() // Total points
      expect(screen.getByText('5')).toBeInTheDocument() // Completed missions
      expect(screen.getByText('3 days')).toBeInTheDocument() // Streak
    })
  })

  test('shows earned achievements', async () => {
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          achievements: {
            earned: [
              {
                id: 'first_mission',
                title: 'Mission Starter',
                description: 'Complete your first mission',
                icon: '🚀',
                rarity: 'common',
                points: 50,
                progress: 100,
              },
            ],
            available: [],
          },
          stats: {
            completedMissions: 1,
            totalPointsEarned: 50,
            consecutiveDays: 1,
          },
        },
      }),
    })
    
    render(<MissionAchievements {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Mission Starter')).toBeInTheDocument()
      expect(screen.getByText('Complete your first mission')).toBeInTheDocument()
      expect(screen.getByText('+50 pts')).toBeInTheDocument()
    })
  })

  test('handles check new achievements', async () => {
    ;(fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: {
            achievements: { earned: [], available: [] },
            stats: {},
          },
        }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: {
            newAchievements: [],
            count: 0,
          },
        }),
      })
    
    render(<MissionAchievements {...mockProps} />)
    
    await waitFor(() => {
      const checkButton = screen.getByText('Check New Achievements')
      fireEvent.click(checkButton)
    })
    
    expect(fetch).toHaveBeenCalledWith('/api/student/mission-achievements/check', {
      method: 'POST',
    })
  })
})

describe('Integration Tests', () => {
  test('roadmap workflow integration', async () => {
    // This would test the complete workflow:
    // 1. Admin creates roadmap
    // 2. Student views roadmap
    // 3. Student completes mission
    // 4. Achievement is unlocked
    // 5. Notification is sent
    
    // For now, we'll just verify the components can work together
    expect(true).toBe(true) // Placeholder for integration tests
  })
})
