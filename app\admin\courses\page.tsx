'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  PlusIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  AcademicCapIcon,
  UsersIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  category?: string
  level?: string
  thumbnailImage?: string
  isPublished: boolean
  isActive: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  instructor: {
    id: string
    name: string
    email: string
    image?: string
  }
  courseCategory?: {
    id: string
    name: string
    slug: string
  }
  totalLessons: number
  totalDuration: number
  enrollmentCount: number
  studentsCount?: number
  rating?: number
}



export default function AdminCoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedLevel, setSelectedLevel] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  const [categories, setCategories] = useState<{ id: string; name: string }[]>([])
  const levels = ['Beginner', 'Intermediate', 'Advanced']
  const statuses = [
    { value: 'all', label: 'All Courses' },
    { value: 'published', label: 'Published' },
    { value: 'draft', label: 'Draft' }
  ]


  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    fetchCourses()
  }, [currentPage, searchQuery, selectedCategory, selectedLevel, selectedStatus])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/courses/categories')
      if (!response.ok) throw new Error('Failed to fetch categories')
      const result = await response.json()
      const data = result.data || result
      setCategories(data.categories || [])
      // If only one category, auto-select it
      if ((data.categories || []).length === 1) {
        setSelectedCategory(data.categories[0].id)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to load categories')
    }
  }

  const fetchCourses = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        ...(searchQuery && { search: searchQuery }),
        ...(selectedCategory !== 'all' && selectedCategory !== '' && { category: selectedCategory }),
        ...(selectedLevel !== 'all' && { level: selectedLevel }),
        ...(selectedStatus !== 'all' && { status: selectedStatus })
      })

      const response = await fetch(`/api/admin/courses?${params}`)
      if (!response.ok) throw new Error('Failed to fetch courses')

      const result = await response.json()
      const data = result.data || result // Handle APIResponse format
      setCourses(data.courses || [])
      setPagination(data.pagination || {
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      })
    } catch (error) {
      console.error('Error fetching courses:', error)
      toast.error('Failed to load courses')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCourse = async (courseId: string) => {
    if (!confirm('Are you sure you want to delete this course?')) return

    try {
      const response = await fetch(`/api/admin/courses/${courseId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete course')

      const data = await response.json()
      toast.success(data.message)
      fetchCourses()
    } catch (error) {
      console.error('Error deleting course:', error)
      toast.error('Failed to delete course')
    }
  }

  const togglePublishStatus = async (courseId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/courses/${courseId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: !currentStatus })
      })

      if (!response.ok) throw new Error('Failed to update course')

      const data = await response.json()
      toast.success(data.message)
      fetchCourses()
    } catch (error) {
      console.error('Error updating course:', error)
      toast.error('Failed to update course')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Course Management
              </h1>
              <p className="text-gray-600 mt-1">Create and manage your courses</p>
            </div>
            <Link href="/admin/courses/create">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                Create Course
              </motion.button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search courses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>{category.name}</option>
              ))}
            </select>

            {/* Level Filter */}
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              <option value="all">All Levels</option>
              {levels.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              {statuses.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Courses Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6 animate-pulse">
                <div className="h-48 bg-gray-200 rounded-xl mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : !courses || courses.length === 0 ? (
          <div className="text-center py-12">
            <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No courses found</h3>
            <p className="text-gray-500 mb-6">Get started by creating your first course</p>
            <Link href="/admin/courses/create">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                Create Course
              </motion.button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {(courses || []).map((course, index) => (
                <CourseCard
                  key={course.id}
                  course={course}
                  index={index}
                  onDelete={handleDeleteCourse}
                  onTogglePublish={togglePublishStatus}
                />
              ))}
            </AnimatePresence>
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={!pagination.hasPrev}
                className="px-4 py-2 bg-white/60 backdrop-blur-xl border border-white/20 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/80 transition-all duration-200"
              >
                Previous
              </button>
              
              {[...Array(pagination.totalPages)].map((_, i) => (
                <button
                  key={i + 1}
                  onClick={() => setCurrentPage(i + 1)}
                  className={`px-4 py-2 rounded-xl transition-all duration-200 ${
                    currentPage === i + 1
                      ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
                      : 'bg-white/60 backdrop-blur-xl border border-white/20 hover:bg-white/80'
                  }`}
                >
                  {i + 1}
                </button>
              ))}
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                disabled={!pagination.hasNext}
                className="px-4 py-2 bg-white/60 backdrop-blur-xl border border-white/20 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/80 transition-all duration-200"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

interface CourseCardProps {
  course: Course
  index: number
  onDelete: (courseId: string) => void
  onTogglePublish: (courseId: string, currentStatus: boolean) => void
}

function CourseCard({ course, index, onDelete, onTogglePublish }: CourseCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="group bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300"
    >
      {/* Course Image */}
      <div className="relative h-48 bg-gradient-to-br from-blue-500 to-indigo-600 overflow-hidden">
        {course.thumbnailImage ? (
          <img
            src={course.thumbnailImage}
            alt={course.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <AcademicCapIcon className="w-16 h-16 text-white/50" />
          </div>
        )}

        {/* Status Badge */}
        <div className="absolute top-4 left-4">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            course.isPublished
              ? 'bg-green-500/90 text-white'
              : 'bg-yellow-500/90 text-white'
          }`}>
            {course.isPublished ? 'Published' : 'Draft'}
          </span>
        </div>

        {/* Price Badge */}
        {course.price > 0 && (
          <div className="absolute top-4 right-4">
            <span className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-sm font-semibold">
              ${course.price}
            </span>
          </div>
        )}
      </div>

      {/* Course Content */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-800 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
            {course.title}
          </h3>
        </div>

        {course.shortDescription && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {course.shortDescription}
          </p>
        )}

        {/* Course Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <UsersIcon className="w-4 h-4 mr-1" />
              <span>{course.enrollmentCount || 0}</span>
            </div>
            <div className="flex items-center">
              <ClockIcon className="w-4 h-4 mr-1" />
              <span>{course.totalDuration}m</span>
            </div>
            <div className="flex items-center">
              <AcademicCapIcon className="w-4 h-4 mr-1" />
              <span>{course.totalLessons}</span>
            </div>
          </div>
          {course.rating && (
            <div className="flex items-center">
              <StarIcon className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
              <span>{course.rating.toFixed(1)}</span>
            </div>
          )}
        </div>

        {/* Course Meta */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <span>{course.courseCategory?.name || course.category || 'Uncategorized'}</span>
          <span>{course.level}</span>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <Link href={`/admin/courses/${course.id}`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                title="View Course"
              >
                <EyeIcon className="w-4 h-4" />
              </motion.button>
            </Link>
            <Link href={`/admin/courses/${course.id}/edit`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                title="Edit Course"
              >
                <PencilIcon className="w-4 h-4" />
              </motion.button>
            </Link>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onDelete(course.id)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
              title="Delete Course"
            >
              <TrashIcon className="w-4 h-4" />
            </motion.button>
          </div>

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => onTogglePublish(course.id, course.isPublished)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              course.isPublished
                ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                : 'bg-green-100 text-green-700 hover:bg-green-200'
            }`}
          >
            {course.isPublished ? 'Unpublish' : 'Publish'}
          </motion.button>
        </div>
      </div>
    </motion.div>
  )
}
